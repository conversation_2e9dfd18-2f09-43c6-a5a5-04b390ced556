import { useState, useEffect } from 'react';
import { YouTubeVideoCard } from './youtube-video-card';
import { getLatestVideos, YouTubeVideo } from '@/lib/youtube-api';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';

export function YouTubeSection() {
  const [clicksVideos, setClicksVideos] = useState<YouTubeVideo[]>([]);
  const [clinicVideos, setClinicVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const [clicks, clinic] = await Promise.all([
          getLatestVideos('clicks-commerce', 6),
          getLatestVideos('conversion-clinic', 6)
        ]);

        setClicksVideos(clicks);
        setClinicVideos(clinic);
      } catch (error) {
        console.error('Error loading videos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);



  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="space-y-3">
            <Skeleton className="aspect-video rounded-lg" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <Tabs defaultValue="clicks" className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-8">
        <TabsTrigger value="clicks">Clicks and Commerce</TabsTrigger>
        <TabsTrigger value="clinic">Conversion Clinic</TabsTrigger>
      </TabsList>
      
      <TabsContent value="clicks">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {clicksVideos.map((video) => (
            <YouTubeVideoCard key={video.id} video={video} />
          ))}
        </div>
        {clicksVideos.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            No videos found for Clicks and Commerce
          </div>
        )}
      </TabsContent>
      
      <TabsContent value="clinic">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {clinicVideos.map((video) => (
            <YouTubeVideoCard key={video.id} video={video} />
          ))}
        </div>
        {clinicVideos.length === 0 && !loading && (
          <div className="text-center py-8 text-muted-foreground">
            No videos found for Conversion Clinic
          </div>
        )}
      </TabsContent>
    </Tabs>
  );
}

# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## YouTube Integration

This project includes a YouTube integration that displays the latest videos from two channels:
- **Clicks and Commerce** (UCVODvnU5ryo8hXkxxbZLDVA)
- **Yananai | Ecom Profit Partner** (UC3VnRCSrpdjBMSR8oXaiByg) - where Conversion Clinic videos are posted

### Setup YouTube API

1. **Get a YouTube Data API v3 key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
   - Create a new project or select an existing one
   - Enable the YouTube Data API v3
   - Create credentials (API key)
   - Restrict the API key to YouTube Data API v3 for security

2. **Configure the environment:**
   - Copy `.env.example` to `.env`
   - Add your API key: `VITE_YOUTUBE_API_KEY=your_api_key_here`

3. **Features:**
   - Displays latest 6 videos from each channel
   - Tabbed interface to switch between channels
   - Responsive grid layout
   - Click to open videos in new tab
   - Loading states and error handling
   - Fallback message when API key is not configured

### Components

- `src/lib/youtube-api.ts` - API service for fetching YouTube data
- `src/components/ui/youtube-section.tsx` - Main section with tabs
- `src/components/ui/youtube-video-card.tsx` - Individual video card component

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

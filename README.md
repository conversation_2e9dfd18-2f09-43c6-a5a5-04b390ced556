# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## YouTube Integration

This project includes a YouTube integration that displays the latest videos from two channels in separate sections:

### Page Structure
1. **Featured Section** containing:
   - **Blog** - Latest 4 blog articles (links to full blog page)
   - **Conversion Clinic** - Latest 3 videos from Yananai | Ecom Profit Partner (UC3VnRCSrpdjBMSR8oXaiByg)
   - **Clicks and Commerce** - Latest 3 videos from Clicks and Commerce (UCVODvnU5ryo8hXkxxbZLDVA)

## Blog System

### Features
- **Full Blog Page** (`/blog`) with category filtering
- **Individual Article Pages** (`/blog/[slug]`)
- **6 Categories**: AI Search, Conversion Rate Optimization, Google Ads, YouTube Ads, SEO, Marketing Strategy
- **Featured Article** display with larger layout
- **Category Tabs** with "All Articles" default view
- **Responsive Design**: 3 articles per row (desktop), 2 per row (tablet), 1 per row (mobile)
- **Interactive Cards** with hover effects and "Read more" functionality
- **Reading Time** calculation and display
- **Related Articles** section on individual article pages
- **SEO-Ready** structure with proper meta tags and URL structure
- **Supabase Integration** with fallback to static content
- **Admin Functions** for creating, updating, and deleting articles

### Blog Components
- `src/pages/Blog.tsx` - Main blog listing page
- `src/pages/BlogArticle.tsx` - Individual article page
- `src/components/ui/blog-card.tsx` - Article card component
- `src/components/ui/blog-featured-card.tsx` - Featured article card
- `src/components/ui/blog-category-tabs.tsx` - Category filter tabs
- `src/data/blog-articles.ts` - Static blog content (fallback)
- `src/lib/blog-api.ts` - Hybrid blog API (Supabase + static fallback)
- `src/lib/blog-supabase-api.ts` - Pure Supabase API functions
- `src/lib/supabase.ts` - Supabase client configuration
- `src/lib/reading-time.ts` - Reading time calculation
- `src/lib/markdown-parser.ts` - Simple markdown to HTML parser

### Supabase Integration
- **Database**: PostgreSQL with optimized schema and indexes
- **Real-time**: Automatic updates when articles are added/modified
- **Fallback**: Graceful degradation to static content if Supabase is unavailable
- **Admin Functions**: Create, update, delete articles programmatically
- **Security**: Row Level Security (RLS) policies for data protection
- **Performance**: Efficient queries with proper indexing

### Setup Instructions
1. **Supabase Setup**: Follow `SUPABASE_SETUP.md` for complete setup guide
2. **Environment Variables**: Configure `.env` with Supabase credentials
3. **Database Schema**: Run `supabase-schema.sql` in your Supabase project
4. **Test Integration**: Run `npm run test:supabase` to verify setup

### Setup YouTube API

1. **Get a YouTube Data API v3 key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
   - Create a new project or select an existing one
   - Enable the YouTube Data API v3
   - Create credentials (API key)
   - **IMPORTANT**: Restrict the API key to YouTube Data API v3 for security

2. **Configure the environment:**
   - Copy `.env.example` to `.env`
   - Add your API key: `VITE_YOUTUBE_API_KEY=your_api_key_here`

3. **Security Best Practices Implemented:**
   - **Rate Limiting**: Max 10 requests per minute to prevent abuse
   - **Caching**: 5-minute cache to reduce API calls
   - **Input Validation**: maxResults limited to 1-50 range
   - **Timeout Protection**: 10-second request timeout
   - **Fallback System**: Mock data when API fails or rate limited
   - **User-Agent Header**: Identifies requests from your website

### Features

- **Responsive Design**: Desktop shows 3-column grid, mobile shows carousel
- **Mobile Carousel**: Reduces scrolling on mobile devices
- **Separate Sections**: Each channel has its own dedicated section
- **Loading States**: Skeleton loading while fetching data
- **Error Handling**: Graceful fallbacks if API fails
- **Mock Data**: Works without API key for development
- **Click to Play**: Opens videos in new tab

### Components

- `src/lib/youtube-api.ts` - API service for fetching YouTube data with security features
- `src/components/ui/conversion-clinic-videos.tsx` - Conversion Clinic videos section (3 latest)
- `src/components/ui/clicks-commerce-videos.tsx` - Clicks and Commerce videos section (3 latest)
- `src/components/ui/youtube-video-card.tsx` - Individual video card component

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/6e12eedb-33b2-4cba-9f80-c0aa6033d446) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

import { TestimonialsColumn } from "@/components/ui/testimonials-columns-1";
import { motion } from "motion/react";

const testimonials = [
  {
    text: "Excellent experience with <PERSON><PERSON><PERSON> and the whole team. Insightful, helpful and patient group of people.",
    image: "/images/testimonials/t1.webp",
    name: "<PERSON>",
    role: "CEO & Founder - Bunkie Life",
  },
   {
    text: "<PERSON><PERSON><PERSON> has a sixth sense for finding growth opportunities. He spotted gaps in our strategy no one else saw and helped us scale BEFORE increasing our budget.",
    image: "/images/testimonials/t2.webp",
    name: "<PERSON>",
    role: "Co-Founder - <PERSON><PERSON>",
  },
  {
    text: "Every recommendation felt tailored to our business, and every week we saw measurable improvements.",
    image: "/images/testimonials/t3.webp",
    name: "<PERSON>iselle",
    role: "Marketing Director - <PERSON><PERSON><PERSON>",
  },
  {
    text: "I don't usually leave testimonials, but the results were too good not to share. This was our most profitable Q4 yet. Hire him if you want to win.",
    image: "/images/testimonials/t4.webp",
    name: "<PERSON>",
    role: "CEO & Founder <PERSON> <PERSON><PERSON><PERSON>",
  },
  {
    text: "I was skeptical about switching to Google Ads, but this team made it painless. No hand-holding, no fluff—just results.",
    image: "/images/testimonials/t5.webp",
    name: "Roger H",
    role: "Owner - Jewellery Brand",
  },
    {
    text: "Google Ads was always a headache until FunnelVision took over. We're now seeing consistent growth every quarter.",
    image: "/images/testimonials/t6.webp",
    name: "Liam",
    role: "VP Marketing - Adversa",
  },
    {
    text: "FunnelVision are not just an extension of your marketing team as a paid search agency, but they are collaborative, proactive partners.",
    image: "/images/testimonials/t7.webp",
    name: "Ayanda",
    role: "Head of Demand Generation",
  },
  {
    text: "Our CAC dropped overnight, and our revenue soared. There is now more synergy between marketing and sales efforts.",
    image: "/images/testimonials/t8.webp",
    name: "Peeter Kuum",
    role: "Sales Director - Wermo",
  },
    {
    text: "Take it from me: they care. They really do. Highly recommend, that's all I can say!",
    image: "/images/testimonials/t9.webp",
    name: "Priscilla Vasquez",
    role: "Ecommerce & Digital Lead - Florent",
  },
];

// Split testimonials into three columns for the infinite scroll effect
const firstColumn = testimonials.slice(0, 3); // Testimonials 1-3
const secondColumn = testimonials.slice(3, 6); // Testimonials 4-6
const thirdColumn = testimonials.slice(6, 9); // Testimonials 7-9

export const TestimonialsDemo = () => {
  return (
    <section className="bg-background my-20 relative">
      <div className="container z-10 mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="flex flex-col items-center justify-center max-w-[540px] mx-auto"
        >
          <div className="flex justify-center">
            <div className="border py-1 px-4 rounded-lg">Testimonials</div>
          </div>

          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter mt-5">
            What our clients <br />
            say about us
          </h2>
          <p className="text-center mt-5 opacity-75">
            We'd rather let our clients toot our horn for us.
          </p>
        </motion.div>

        <div className="flex justify-center gap-6 mt-10 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[740px] overflow-hidden">
          <TestimonialsColumn testimonials={firstColumn} duration={17.5} />
          <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={17.5} />
          <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={17.5} />
        </div>
      </div>
    </section>
  );
};
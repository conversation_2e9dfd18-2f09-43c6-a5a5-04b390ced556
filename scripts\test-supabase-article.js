/**
 * Test script to add a new article to Supabase
 * Run with: node scripts/test-supabase-article.js
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please set VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test article data
const testArticle = {
  slug: 'test-supabase-integration-2024',
  title: 'Testing Supabase Integration for FunnelVision Blog',
  description: 'A test article to verify that our Supabase integration is working correctly for the FunnelVision blog system.',
  content: `
# Testing Supabase Integration

This is a test article created via our Supabase integration script to verify that the blog system is working correctly.

## Features Being Tested

### 1. Article Creation
- Creating articles programmatically
- Automatic reading time calculation
- Category assignment
- Author information

### 2. Database Integration
- Supabase connection
- Data validation
- Error handling
- Fallback mechanisms

### 3. Content Management
- Rich text content support
- Markdown parsing
- Image handling
- SEO optimization

## Technical Implementation

The blog system uses:
- **Supabase** for database management
- **React** for the frontend
- **TypeScript** for type safety
- **Tailwind CSS** for styling

### Database Schema
Our articles table includes:
- Unique slugs for SEO-friendly URLs
- Category classification
- Reading time calculation
- Author information
- Publication timestamps

## Next Steps

If you're seeing this article in the blog, it means:
1. ✅ Supabase connection is working
2. ✅ Article creation is functional
3. ✅ Database queries are successful
4. ✅ Frontend integration is complete

This test confirms that the blog system is ready for production use!
  `,
  thumbnail: '/placeholder.svg',
  published_at: new Date().toISOString(),
  reading_time: 3,
  category: 'marketing-strategy',
  author_name: 'Yana Nai',
  author_avatar: '/placeholder.svg',
  tags: ['Supabase', 'Testing', 'Blog System', 'Integration'],
  featured: false
};

async function testArticleCreation() {
  console.log('🚀 Testing Supabase article creation...');
  
  try {
    // First, check if article with this slug already exists
    const { data: existing } = await supabase
      .from('blog_articles')
      .select('id')
      .eq('slug', testArticle.slug)
      .single();

    if (existing) {
      console.log('📝 Article already exists, updating instead...');
      
      const { data, error } = await supabase
        .from('blog_articles')
        .update(testArticle)
        .eq('slug', testArticle.slug)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating article:', error);
        return;
      }

      console.log('✅ Article updated successfully!');
      console.log('📄 Article details:', {
        id: data.id,
        title: data.title,
        slug: data.slug,
        category: data.category,
        reading_time: data.reading_time
      });
    } else {
      console.log('📝 Creating new article...');
      
      const { data, error } = await supabase
        .from('blog_articles')
        .insert([testArticle])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating article:', error);
        return;
      }

      console.log('✅ Article created successfully!');
      console.log('📄 Article details:', {
        id: data.id,
        title: data.title,
        slug: data.slug,
        category: data.category,
        reading_time: data.reading_time
      });
    }

    // Test fetching the article
    console.log('\n🔍 Testing article retrieval...');
    const { data: retrieved, error: fetchError } = await supabase
      .from('blog_articles')
      .select('*')
      .eq('slug', testArticle.slug)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching article:', fetchError);
      return;
    }

    console.log('✅ Article retrieved successfully!');
    console.log('📖 Retrieved article:', {
      title: retrieved.title,
      category: retrieved.category,
      published_at: retrieved.published_at,
      tags: retrieved.tags
    });

    // Test fetching all articles
    console.log('\n📚 Testing all articles retrieval...');
    const { data: allArticles, error: allError } = await supabase
      .from('blog_articles')
      .select('id, title, slug, category')
      .order('published_at', { ascending: false })
      .limit(5);

    if (allError) {
      console.error('❌ Error fetching all articles:', allError);
      return;
    }

    console.log('✅ All articles retrieved successfully!');
    console.log('📋 Recent articles:', allArticles.map(a => ({
      title: a.title,
      slug: a.slug,
      category: a.category
    })));

    console.log('\n🎉 All tests passed! Supabase integration is working correctly.');
    console.log(`🌐 You can view the test article at: /blog/${testArticle.slug}`);

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the test
testArticleCreation();

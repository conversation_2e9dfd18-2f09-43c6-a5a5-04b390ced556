import { useState, useEffect } from 'react';
import { BlogCategory } from '@/types/blog';
import { getAllArticles, getArticlesByCategory, getFeaturedArticle } from '@/lib/blog-api';
import { BlogFeaturedCard } from '@/components/ui/blog-featured-card';
import { BlogCard } from '@/components/ui/blog-card';
import { BlogCategoryTabs } from '@/components/ui/blog-category-tabs';
import { EnhancedNavbar } from '@/components/ui/enhanced-navbar';
import { Footer2 } from '@/components/ui/shadcnblocks-com-footer2';

export default function Blog() {
  const [activeCategory, setActiveCategory] = useState<'all' | BlogCategory>('all');
  const [articles, setArticles] = useState(getAllArticles());
  const featuredArticle = getFeaturedArticle();

  useEffect(() => {
    if (activeCategory === 'all') {
      setArticles(getAllArticles());
    } else {
      setArticles(getArticlesByCategory(activeCategory));
    }
  }, [activeCategory]);

  // Filter out featured article from regular articles list
  const regularArticles = articles.filter(article => article.id !== featuredArticle?.id);

  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
                FunnelVision Blog
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Expert insights on conversion optimization, digital marketing, and growth strategies 
                to help you scale your business.
              </p>
            </div>

            {/* Featured Article */}
            {featuredArticle && (
              <BlogFeaturedCard article={featuredArticle} />
            )}

            {/* Category Tabs */}
            <BlogCategoryTabs
              activeCategory={activeCategory}
              onCategoryChange={setActiveCategory}
            />

            {/* Articles Grid */}
            {regularArticles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {regularArticles.map((article) => (
                  <BlogCard key={article.id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <p className="text-lg text-muted-foreground">
                  No articles found in this category yet.
                </p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer2 />
    </div>
  );
}

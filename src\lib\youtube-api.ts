import axios from 'axios';

const YOUTUBE_API_KEY = import.meta.env.VITE_YOUTUBE_API_KEY;

const CHANNELS = {
  'clicks-commerce': 'UCVODvnU5ryo8hXkxxbZLDVA',
  'conversion-clinic': 'UC3VnRCSrpdjBMSR8oXaiByg'
} as const;

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  viewCount?: string;
  url: string;
}

// Mock data for development/demo purposes
const MOCK_VIDEOS: Record<keyof typeof CHANNELS, YouTubeVideo[]> = {
  'clicks-commerce': [
    {
      id: 'demo1',
      title: 'How to Optimize Your E-commerce Conversion Rate',
      description: 'Learn the top strategies to increase your online store conversions...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      channelTitle: 'Clicks and Commerce',
      url: 'https://youtube.com/watch?v=demo1'
    },
    {
      id: 'demo2',
      title: 'Facebook Ads Strategy for E-commerce Success',
      description: 'Master Facebook advertising for your online business...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      channelTitle: 'Clicks and Commerce',
      url: 'https://youtube.com/watch?v=demo2'
    },
    {
      id: 'demo3',
      title: 'Email Marketing Automation for Online Stores',
      description: 'Set up automated email sequences that convert...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
      channelTitle: 'Clicks and Commerce',
      url: 'https://youtube.com/watch?v=demo3'
    }
  ],
  'conversion-clinic': [
    {
      id: 'clinic1',
      title: 'Conversion Clinic: Landing Page Optimization',
      description: 'Analyzing and improving landing page performance...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
      channelTitle: 'Yananai | Ecom Profit Partner',
      url: 'https://youtube.com/watch?v=clinic1'
    },
    {
      id: 'clinic2',
      title: 'Conversion Clinic: Product Page Analysis',
      description: 'Deep dive into product page conversion optimization...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 129600000).toISOString(), // 1.5 days ago
      channelTitle: 'Yananai | Ecom Profit Partner',
      url: 'https://youtube.com/watch?v=clinic2'
    },
    {
      id: 'clinic3',
      title: 'Conversion Clinic: Checkout Flow Optimization',
      description: 'Streamlining the checkout process for better conversions...',
      thumbnail: '/placeholder.svg',
      publishedAt: new Date(Date.now() - 216000000).toISOString(), // 2.5 days ago
      channelTitle: 'Yananai | Ecom Profit Partner',
      url: 'https://youtube.com/watch?v=clinic3'
    }
  ]
};

export async function getLatestVideos(
  channelKey: keyof typeof CHANNELS,
  maxResults = 6
): Promise<YouTubeVideo[]> {
  const channelId = CHANNELS[channelKey];

  if (!YOUTUBE_API_KEY) {
    console.warn('YouTube API key not found. Using mock data for development.');
    return MOCK_VIDEOS[channelKey].slice(0, maxResults);
  }
  
  try {
    const response = await axios.get(
      `https://www.googleapis.com/youtube/v3/search?key=${YOUTUBE_API_KEY}&channelId=${channelId}&part=snippet&order=date&maxResults=${maxResults}&type=video`
    );
    
    return response.data.items.map((item: any) => ({
      id: item.id.videoId,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.medium.url,
      publishedAt: item.snippet.publishedAt,
      channelTitle: item.snippet.channelTitle,
      url: `https://www.youtube.com/watch?v=${item.id.videoId}`
    }));
  } catch (error) {
    console.error('Error fetching YouTube videos:', error);
    return [];
  }
}

export async function getAllLatestVideos(maxResults = 6): Promise<YouTubeVideo[]> {
  try {
    const [clicksVideos, clinicVideos] = await Promise.all([
      getLatestVideos('clicks-commerce', maxResults),
      getLatestVideos('conversion-clinic', maxResults)
    ]);
    
    // Combine and sort by publish date
    const allVideos = [...clicksVideos, ...clinicVideos];
    return allVideos
      .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
      .slice(0, maxResults);
  } catch (error) {
    console.error('Error fetching all YouTube videos:', error);
    return [];
  }
}

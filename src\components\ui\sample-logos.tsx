import React from "react";

// Sample logo components for the carousel
export const Logo1: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <circle cx="50" cy="50" r="40" className="fill-primary" />
    <text x="50" y="55" textAnchor="middle" className="fill-primary-foreground text-lg font-bold">A</text>
  </svg>
);

export const Logo2: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <rect x="10" y="10" width="80" height="80" rx="10" className="fill-secondary" />
    <text x="50" y="55" textAnchor="middle" className="fill-secondary-foreground text-lg font-bold">B</text>
  </svg>
);

export const Logo3: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <polygon points="50,10 90,90 10,90" className="fill-accent" />
    <text x="50" y="65" textAnchor="middle" className="fill-accent-foreground text-lg font-bold">C</text>
  </svg>
);

export const Logo4: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <ellipse cx="50" cy="50" rx="45" ry="25" className="fill-muted" />
    <text x="50" y="55" textAnchor="middle" className="fill-muted-foreground text-lg font-bold">D</text>
  </svg>
);

export const Logo5: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <path d="M50 10 L90 50 L50 90 L10 50 Z" className="fill-primary" />
    <text x="50" y="55" textAnchor="middle" className="fill-primary-foreground text-lg font-bold">E</text>
  </svg>
);

export const Logo6: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <rect x="20" y="20" width="60" height="60" className="fill-secondary" />
    <circle cx="50" cy="50" r="20" className="fill-background" />
    <text x="50" y="55" textAnchor="middle" className="fill-secondary text-sm font-bold">F</text>
  </svg>
);

// Sample logos array for the carousel
export const sampleLogos = [
  { name: "Company A", id: 1, img: Logo1 },
  { name: "Company B", id: 2, img: Logo2 },
  { name: "Company C", id: 3, img: Logo3 },
  { name: "Company D", id: 4, img: Logo4 },
  { name: "Company E", id: 5, img: Logo5 },
  { name: "Company F", id: 6, img: Logo6 },
];

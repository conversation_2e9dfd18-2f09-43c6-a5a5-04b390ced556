import React from "react";

// Sample logo components for the carousel
export const Logo1: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <circle cx="50" cy="50" r="40" className="fill-primary" />
    <text x="50" y="55" textAnchor="middle" className="fill-primary-foreground text-lg font-bold">A</text>
  </svg>
);

export const Logo2: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <rect x="10" y="10" width="80" height="80" rx="10" className="fill-secondary" />
    <text x="50" y="55" textAnchor="middle" className="fill-secondary-foreground text-lg font-bold">B</text>
  </svg>
);

export const Logo3: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <polygon points="50,10 90,90 10,90" className="fill-accent" />
    <text x="50" y="65" textAnchor="middle" className="fill-accent-foreground text-lg font-bold">C</text>
  </svg>
);

export const Logo4: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <ellipse cx="50" cy="50" rx="45" ry="25" className="fill-muted" />
    <text x="50" y="55" textAnchor="middle" className="fill-muted-foreground text-lg font-bold">D</text>
  </svg>
);

export const Logo5: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <path d="M50 10 L90 50 L50 90 L10 50 Z" className="fill-primary" />
    <text x="50" y="55" textAnchor="middle" className="fill-primary-foreground text-lg font-bold">E</text>
  </svg>
);

export const Logo6: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 100 100" fill="currentColor" {...props}>
    <rect x="20" y="20" width="60" height="60" className="fill-secondary" />
    <circle cx="50" cy="50" r="20" className="fill-background" />
    <text x="50" y="55" textAnchor="middle" className="fill-secondary text-sm font-bold">F</text>
  </svg>
);

// Client logos for the carousel (using actual client logos 1-12)
const ClientLogo = ({ src, alt, ...props }: { src: string; alt: string } & React.SVGProps<SVGSVGElement>) => (
  <img src={src} alt={alt} className="h-20 w-20 max-h-[80%] max-w-[80%] object-contain md:h-32 md:w-32" {...props} />
);

// Sample logos array for the carousel
export const sampleLogos = [
  { name: "Client 1", id: 1, img: () => <ClientLogo src="/images/clients/1.svg" alt="Client 1" /> },
  { name: "Client 2", id: 2, img: () => <ClientLogo src="/images/clients/2.svg" alt="Client 2" /> },
  { name: "Client 3", id: 3, img: () => <ClientLogo src="/images/clients/3.svg" alt="Client 3" /> },
  { name: "Client 4", id: 4, img: () => <ClientLogo src="/images/clients/4.svg" alt="Client 4" /> },
  { name: "Client 5", id: 5, img: () => <ClientLogo src="/images/clients/5.svg" alt="Client 5" /> },
  { name: "Client 6", id: 6, img: () => <ClientLogo src="/images/clients/6.svg" alt="Client 6" /> },
  { name: "Client 7", id: 7, img: () => <ClientLogo src="/images/clients/7.svg" alt="Client 7" /> },
  { name: "Client 8", id: 8, img: () => <ClientLogo src="/images/clients/8.svg" alt="Client 8" /> },
  { name: "Client 9", id: 9, img: () => <ClientLogo src="/images/clients/9.svg" alt="Client 9" /> },
  { name: "Client 10", id: 10, img: () => <ClientLogo src="/images/clients/10.svg" alt="Client 10" /> },
  { name: "Client 11", id: 11, img: () => <ClientLogo src="/images/clients/11.svg" alt="Client 11" /> },
  { name: "Client 12", id: 12, img: () => <ClientLogo src="/images/clients/12.svg" alt="Client 12" /> },
];

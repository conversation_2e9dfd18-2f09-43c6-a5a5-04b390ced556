export interface BlogArticle {
  id: string;
  slug: string;
  title: string;
  description: string;
  content: string;
  thumbnail: string;
  publishedAt: string;
  readingTime: number;
  category: BlogCategory;
  author: {
    name: string;
    avatar?: string;
  };
  tags?: string[];
  featured?: boolean;
}

export type BlogCategory = 
  | 'ai-search' 
  | 'conversion-rate-optimization' 
  | 'google-ads' 
  | 'youtube-ads' 
  | 'seo' 
  | 'marketing-strategy';

export interface BlogCategoryInfo {
  id: BlogCategory;
  name: string;
  slug: string;
  description: string;
}

export const BLOG_CATEGORIES: BlogCategoryInfo[] = [
  {
    id: 'ai-search',
    name: 'AI Search',
    slug: 'ai-search',
    description: 'Latest insights on AI-powered search optimization'
  },
  {
    id: 'conversion-rate-optimization',
    name: 'Conversion Rate Optimization',
    slug: 'conversion-rate-optimization',
    description: 'Strategies to improve your website conversion rates'
  },
  {
    id: 'google-ads',
    name: 'Google Ads',
    slug: 'google-ads',
    description: 'Google Ads strategies and best practices'
  },
  {
    id: 'youtube-ads',
    name: 'YouTube Ads',
    slug: 'youtube-ads',
    description: 'YouTube advertising tips and techniques'
  },
  {
    id: 'seo',
    name: 'SEO',
    slug: 'seo',
    description: 'Search engine optimization strategies'
  },
  {
    id: 'marketing-strategy',
    name: 'Marketing Strategy',
    slug: 'marketing-strategy',
    description: 'Comprehensive marketing strategies and insights'
  }
];

import { useState, useEffect } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { getArticleBySlug } from '@/lib/blog-api';
import { BLOG_CATEGORIES, type BlogArticle } from '@/types/blog';
import { formatReadingTime } from '@/lib/reading-time';
import { parseMarkdown } from '@/lib/markdown-parser';
import { EnhancedNavbar } from '@/components/ui/enhanced-navbar';
import { Footer2 } from '@/components/ui/shadcnblocks-com-footer2';
import { Breadcrumbs } from '@/components/ui/breadcrumbs';
import { AuthorBio } from '@/components/ui/author-bio';
import { ReadNext } from '@/components/ui/read-next';
import { Calendar, Clock, Tag, User } from 'lucide-react';

export default function BlogArticle() {
  const { slug } = useParams<{ slug: string }>();
  const [article, setArticle] = useState<BlogArticle | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!slug) return;

    const loadArticle = async () => {
      setLoading(true);
      try {
        const articleData = await getArticleBySlug(slug);
        setArticle(articleData);
      } catch (error) {
        console.error('Error loading article:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticle();
  }, [slug]);

  if (!slug) {
    return <Navigate to="/blog" replace />;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <EnhancedNavbar />
        <main className="pt-20">
          <section className="py-12 px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-6">
                <div className="h-8 bg-muted rounded animate-pulse" />
                <div className="h-12 bg-muted rounded animate-pulse" />
                <div className="h-6 bg-muted rounded w-2/3 animate-pulse" />
                <div className="aspect-video bg-muted rounded animate-pulse" />
              </div>
            </div>
          </section>
        </main>
        <Footer2 />
      </div>
    );
  }

  if (!article) {
    return <Navigate to="/blog" replace />;
  }

  const categoryInfo = BLOG_CATEGORIES.find(cat => cat.id === article.category);
  
  const publishDate = new Date(article.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      
      <main className="pt-20">
        {/* Article Header */}
        <section className="py-12 px-4">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumbs category={article.category} articleTitle={article.title} />

            {/* Article Meta */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {publishDate}
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {formatReadingTime(article.readingTime)}
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <button
                  onClick={() => document.getElementById('author-bio')?.scrollIntoView({ behavior: 'smooth' })}
                  className="hover:text-primary transition-colors cursor-pointer"
                >
                  {article.author.name}
                </button>
              </div>
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                <span className="bg-primary/10 text-primary px-2 py-1 rounded-md text-xs font-medium">
                  {categoryInfo?.name}
                </span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
              {article.title}
            </h1>

            {/* Description */}
            <p className="text-xl text-muted-foreground leading-relaxed mb-8">
              {article.description}
            </p>

            {/* Featured Image */}
            <div className="relative overflow-hidden rounded-lg aspect-video bg-muted mb-12">
              <img
                src={article.thumbnail}
                alt={article.title}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </section>

        {/* Article Content */}
        <section className="pb-12 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              <div
                className="text-foreground leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: parseMarkdown(article.content)
                }}
              />
            </div>

            {/* Author Bio */}
            <AuthorBio author={article.author} />

            {/* Read Next Section */}
            <ReadNext currentArticle={article} />
          </div>
        </section>
      </main>

      <Footer2 />
    </div>
  );
}

"use client";

import { Carousel } from "@/components/ui/carousel";


export function CarouselDemo() {
  // Define cards data for case studies with modal data (removed 4th card as requested)
  const caseStudyCardsData = [
    {
      title: "Cut wasted ad spend by 52.6%",
      button: "Read case study",
      src: "/images/case-studies/case-1.webp",
      logo: "/images/clients/10.svg",
      ctaLink: "#case-study-boulies",
    },
    {
      title: "Increased monthly revenue 122%",
      button: "Read case study",
      src: "/images/case-studies/case-2.webp",
      logo: "/images/clients/14.png",
      ctaLink: "#case-study-bunkie-life",
    },
    {
      title: "Increased revenue 8.4X in 12 months",
      button: "Read case study",
      src: "/images/case-studies/case-3.webp",
      logo: "/images/clients/13.svg",
      ctaLink: "#case-study-kodiak",
    },
  ];

  return (
    <div className="relative overflow-hidden w-full h-full py-20">
      <Carousel slides={caseStudyCardsData} />
    </div>
  );
}
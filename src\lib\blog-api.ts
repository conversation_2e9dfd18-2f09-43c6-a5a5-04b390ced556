import { BlogArticle, BlogCategory } from '@/types/blog';
import { ALL_ARTICLES } from '@/data/blog-articles';

/**
 * Get all blog articles sorted by publish date (newest first)
 */
export function getAllArticles(): BlogArticle[] {
  return ALL_ARTICLES.sort((a, b) => 
    new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

/**
 * Get articles by category
 */
export function getArticlesByCategory(category: BlogCategory): BlogArticle[] {
  return getAllArticles().filter(article => article.category === category);
}

/**
 * Get featured article (most recent featured article)
 */
export function getFeaturedArticle(): BlogArticle | null {
  const featuredArticles = getAllArticles().filter(article => article.featured);
  return featuredArticles.length > 0 ? featuredArticles[0] : getAllArticles()[0];
}

/**
 * Get article by slug
 */
export function getArticleBySlug(slug: string): BlogArticle | null {
  return ALL_ARTICLES.find(article => article.slug === slug) || null;
}

/**
 * Get related articles (same category, excluding current article)
 */
export function getRelatedArticles(currentArticle: BlogArticle, limit: number = 3): BlogArticle[] {
  return getAllArticles()
    .filter(article => 
      article.category === currentArticle.category && 
      article.id !== currentArticle.id
    )
    .slice(0, limit);
}

/**
 * Get recent articles for homepage (excluding featured)
 */
export function getRecentArticles(limit: number = 4): BlogArticle[] {
  const featured = getFeaturedArticle();
  return getAllArticles()
    .filter(article => article.id !== featured?.id)
    .slice(0, limit);
}

/**
 * Search articles by title and description
 */
export function searchArticles(query: string): BlogArticle[] {
  const searchTerm = query.toLowerCase();
  return getAllArticles().filter(article =>
    article.title.toLowerCase().includes(searchTerm) ||
    article.description.toLowerCase().includes(searchTerm) ||
    article.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

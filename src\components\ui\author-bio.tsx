import { type BlogArticle } from '@/types/blog';

interface AuthorBioProps {
  author: BlogArticle['author'];
}

export function AuthorBio({ author }: AuthorBioProps) {
  if (!author.bio) return null;

  return (
    <div id="author-bio" className="border-t border-border pt-12 mt-16">
      <div className="flex flex-col sm:flex-row gap-6 items-start">
        {/* Author Avatar */}
        <div className="flex-shrink-0">
          <img
            src={author.avatar || '/placeholder.svg'}
            alt={author.name}
            className="w-20 h-20 rounded-full object-cover border-2 border-border"
          />
        </div>
        
        {/* Author Info */}
        <div className="flex-1">
          <div className="mb-3">
            <span className="text-sm text-muted-foreground">Written by</span>
            <h3 className="text-xl font-semibold text-foreground mt-1">
              {author.name}
            </h3>
          </div>
          
          {/* Bio with HTML support for links */}
          <div 
            className="text-muted-foreground leading-relaxed"
            dangerouslySetInnerHTML={{ __html: author.bio }}
          />
        </div>
      </div>
    </div>
  );
}

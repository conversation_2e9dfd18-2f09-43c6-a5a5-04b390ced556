-- Create blog_articles table
CREATE TABLE IF NOT EXISTS blog_articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  slug TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  content TEXT NOT NULL,
  thumbnail TEXT NOT NULL,
  published_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  reading_time INTEGER NOT NULL DEFAULT 1,
  category TEXT NOT NULL CHECK (category IN (
    'ai-search',
    'conversion-rate-optimization', 
    'google-ads',
    'youtube-ads',
    'seo',
    'marketing-strategy'
  )),
  author_name TEXT NOT NULL,
  author_avatar TEXT,
  tags TEXT[],
  featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_articles_category ON blog_articles(category);
CREATE INDEX IF NOT EXISTS idx_blog_articles_published_at ON blog_articles(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_blog_articles_featured ON blog_articles(featured) WHERE featured = TRUE;
CREATE INDEX IF NOT EXISTS idx_blog_articles_slug ON blog_articles(slug);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_blog_articles_updated_at 
    BEFORE UPDATE ON blog_articles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE blog_articles ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access" ON blog_articles
    FOR SELECT USING (true);

-- Create policies for authenticated users to insert/update/delete
-- (You can modify these based on your authentication requirements)
CREATE POLICY "Allow authenticated users to insert" ON blog_articles
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update" ON blog_articles
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to delete" ON blog_articles
    FOR DELETE USING (auth.role() = 'authenticated');

-- Insert sample data (optional - for testing)
INSERT INTO blog_articles (
  slug,
  title,
  description,
  content,
  thumbnail,
  published_at,
  reading_time,
  category,
  author_name,
  author_avatar,
  tags,
  featured
) VALUES (
  'ai-search-revolution-2024',
  'The AI Search Revolution: How Machine Learning is Transforming SEO in 2024',
  'Discover how artificial intelligence is reshaping search algorithms and what it means for your SEO strategy.',
  'The landscape of search engine optimization is undergoing a dramatic transformation. With the integration of artificial intelligence and machine learning algorithms, search engines are becoming more sophisticated in understanding user intent and delivering relevant results.

## The Rise of AI in Search

Google''s RankBrain, BERT, and now MUM (Multitask Unified Model) represent significant leaps forward in how search engines process and understand content. These AI systems can now:

- Understand context and nuance in search queries
- Process multiple languages simultaneously
- Interpret visual and audio content alongside text
- Provide more personalized search results

## Impact on SEO Strategies

Traditional keyword-focused SEO is evolving into a more holistic approach that prioritizes:

### 1. Content Quality and Relevance
AI algorithms can now better assess the quality and relevance of content, making it crucial to focus on creating valuable, comprehensive resources that truly serve user needs.

### 2. User Experience Signals
Core Web Vitals and other UX metrics are becoming increasingly important as AI systems learn to correlate user behavior with content quality.

### 3. Semantic Search Optimization
Understanding the relationships between concepts and entities is now more important than exact keyword matching.

## Preparing for the Future

To stay ahead in this AI-driven landscape, businesses should:

- Invest in high-quality, authoritative content
- Optimize for user experience and site performance
- Focus on topical authority and expertise
- Implement structured data and schema markup
- Monitor and adapt to algorithm updates

The future of SEO lies in understanding and working with AI, not against it. By focusing on user value and technical excellence, businesses can thrive in this new era of search.',
  '/placeholder.svg',
  '2024-01-15T10:00:00Z',
  8,
  'ai-search',
  'Yana Nai',
  '/placeholder.svg',
  ARRAY['AI', 'SEO', 'Machine Learning', 'Google'],
  true
) ON CONFLICT (slug) DO NOTHING;

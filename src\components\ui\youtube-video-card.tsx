import { formatDistanceToNow } from 'date-fns';
import { Play, ExternalLink } from 'lucide-react';
import { YouTubeVideo } from '@/lib/youtube-api';

interface YouTubeVideoCardProps {
  video: YouTubeVideo;
}

export function YouTubeVideoCard({ video }: YouTubeVideoCardProps) {
  const timeAgo = formatDistanceToNow(new Date(video.publishedAt), { addSuffix: true });

  return (
    <div className="group cursor-pointer hover:opacity-90 transition-opacity">
      <a href={video.url} target="_blank" rel="noopener noreferrer">
        <div className="relative overflow-hidden rounded-lg mb-3">
          <img 
            src={video.thumbnail} 
            alt={video.title}
            className="w-full aspect-video object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-red-600 rounded-full p-3 group-hover:scale-110 transition-transform">
              <Play className="w-6 h-6 text-white fill-white" />
            </div>
          </div>
          <div className="absolute top-2 right-2">
            <ExternalLink className="w-4 h-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
        <h3 className="font-semibold text-sm line-clamp-2 mb-2 group-hover:text-primary transition-colors">
          {video.title}
        </h3>
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span className="truncate mr-2">{video.channelTitle}</span>
          <span className="whitespace-nowrap">{timeAgo}</span>
        </div>
      </a>
    </div>
  );
}

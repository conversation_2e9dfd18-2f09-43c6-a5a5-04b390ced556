import { MoveRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { getRecentArticles } from "@/lib/blog-api";
import { BlogCard } from "@/components/ui/blog-card";

function BlogDemo() {
  const recentArticles = getRecentArticles(4);

  return (
    <div className="w-full">
      <div className="container mx-auto flex flex-col gap-14">
        <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
          <h4 className="text-3xl md:text-5xl tracking-tighter max-w-xl font-regular">
            Latest articles
          </h4>
          <Button className="gap-4" onClick={() => window.location.href = '/blog'}>
            View all articles <MoveRight className="w-4 h-4" />
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {recentArticles.map((article) => (
            <BlogCard key={article.id} article={article} />
          ))}
        </div>
      </div>
    </div>
  );
}

export { BlogDemo };

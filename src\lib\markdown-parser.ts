/**
 * Enhanced markdown parser for blog content
 * Converts markdown to HTML with proper spacing and image handling
 */
export function parseMarkdown(content: string): string {
  let html = content;

  // Images with proper spacing
  html = html.replace(/!\[(.*?)\]\((.*?)\)/g, '<div class="my-8"><img src="$2" alt="$1" class="w-full rounded-lg shadow-sm" /></div>');

  // Headers with proper spacing
  html = html.replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-4 mt-10 text-foreground">$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mb-6 mt-12 text-foreground">$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-8 mt-16 text-foreground">$1</h1>');

  // Bold and italic
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-foreground">$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em class="italic">$1</em>');

  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:underline" target="_blank" rel="noopener noreferrer">$1</a>');

  // Code blocks with syntax highlighting
  html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-6 rounded-lg my-8 overflow-x-auto border"><code class="text-sm">$1</code></pre>');
  html = html.replace(/`(.*?)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono">$1</code>');

  // Lists with better spacing
  html = html.replace(/^\- (.*$)/gim, '<li class="mb-3 leading-relaxed">$1</li>');
  html = html.replace(/(<li.*?<\/li>)/gs, '<ul class="list-disc list-inside mb-8 space-y-3 pl-4">$1</ul>');

  // Numbered lists
  html = html.replace(/^\d+\. (.*$)/gim, '<li class="mb-3 leading-relaxed">$1</li>');
  html = html.replace(/(<li.*?<\/li>)/gs, '<ol class="list-decimal list-inside mb-8 space-y-3 pl-4">$1</ol>');

  // Blockquotes
  html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-primary pl-6 py-4 my-8 bg-muted/30 italic text-muted-foreground">$1</blockquote>');

  // Horizontal rules
  html = html.replace(/^---$/gim, '<hr class="my-12 border-border" />');

  // Split into paragraphs and add proper spacing
  const paragraphs = html.split(/\n\s*\n/);
  html = paragraphs
    .map(paragraph => {
      // Skip if it's already a formatted element (starts with <)
      if (paragraph.trim().startsWith('<')) {
        return paragraph.trim();
      }
      // Wrap in paragraph tags with proper spacing
      return `<p class="mb-6 leading-relaxed text-foreground">${paragraph.trim()}</p>`;
    })
    .join('\n\n');

  // Clean up empty paragraphs and extra whitespace
  html = html.replace(/<p class="mb-6 leading-relaxed text-foreground"><\/p>/g, '');
  html = html.replace(/\n{3,}/g, '\n\n');

  return html;
}

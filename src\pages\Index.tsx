import { Hero } from "@/components/ui/animated-hero";
import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { BentoDemo } from "@/components/ui/bento-demo";
import { CarouselDemo } from "@/components/ui/carousel-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { FAQDemo } from "@/components/ui/faq-demo";
import { YouTubeSection } from "@/components/ui/youtube-section";
import { TextGenerateEffectDemo } from "@/components/ui/text-generate-effect-demo";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { Button } from "@/components/ui/button";
import { MoveRight } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <Hero />
      </div>
      <section className="py-20 px-4 bg-muted/10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">The Problem</h2>
            <div className="max-w-3xl mx-auto">
              <TextGenerateEffectDemo />
            </div>
          </div>
        </div>
      </section>
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Services</h2>
            <p className="text-xl text-muted-foreground">Discover what we offer</p>
          </div>
          <BentoDemo />
        </div>
      </section>
      <section className="py-20 px-4 bg-primary/5">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
            Ready to Skyrocket Your Growth?
          </h2>
          <Button size="lg" className="gap-4">
            Work with us <MoveRight className="w-4 h-4" />
          </Button>
        </div>
      </section>
      <section className="py-20 px-4 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Case Studies</h2>
            <p className="text-xl text-muted-foreground">Explore our featured projects</p>
          </div>
          <CarouselDemo />
        </div>
      </section>
      <TestimonialsDemo />
      <CTADemo />
      <FAQDemo />
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Latest Videos</h2>
            <p className="text-xl text-muted-foreground">Fresh insights from our YouTube channels</p>
          </div>
          <YouTubeSection />
        </div>
      </section>
      <Footer2Demo />
    </div>
  );
};

export default Index;

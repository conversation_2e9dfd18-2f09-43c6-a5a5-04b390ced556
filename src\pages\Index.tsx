import { Hero } from "@/components/ui/animated-hero";
import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { BentoDemo } from "@/components/ui/bento-demo";
import { CarouselDemo } from "@/components/ui/carousel-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { FAQDemo } from "@/components/ui/faq-demo";
import { BlogDemo } from "@/components/ui/blog-demo";
import { ConversionClinicSection } from "@/components/ui/conversion-clinic-section";
import { ClicksCommerceSection } from "@/components/ui/clicks-commerce-section";
import { TextGenerateEffectDemo } from "@/components/ui/text-generate-effect-demo";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <EnhancedNavbar />
      <div className="pt-16">
        <Hero />
      </div>
      <section className="py-20 px-4 bg-muted/10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">The Problem</h2>
            <div className="max-w-3xl mx-auto">
              <TextGenerateEffectDemo />
            </div>
          </div>
        </div>
      </section>
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Services</h2>
            <p className="text-xl text-muted-foreground">Discover what we offer</p>
          </div>
          <BentoDemo />
        </div>
      </section>
      <section className="py-20 px-4 bg-primary/5">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
            Ready to Skyrocket Your Growth?
          </h2>
          <Button size="lg" className="gap-4">
            Work with us <MoveRight className="w-4 h-4" />
          </Button>
        </div>
      </section>
      <section className="py-20 px-4 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Case Studies</h2>
            <p className="text-xl text-muted-foreground">Explore our featured projects</p>
          </div>
          <CarouselDemo />
        </div>
      </section>
      <TestimonialsDemo />
      <CTADemo />
      <FAQDemo />
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Featured</h2>
            <p className="text-xl text-muted-foreground">Latest insights and updates</p>
          </div>
          <BlogDemo />
        </div>
      </section>
      <section className="py-20 px-4 bg-muted/10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Conversion Clinic</h2>
            <p className="text-xl text-muted-foreground">Latest episodes from our conversion optimization series</p>
          </div>
          <ConversionClinicSection />
        </div>
      </section>
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Clicks and Commerce</h2>
            <p className="text-xl text-muted-foreground">E-commerce insights and strategies</p>
          </div>
          <ClicksCommerceSection />
        </div>
      </section>
      <Footer2Demo />
    </div>
  );
};

export default Index;

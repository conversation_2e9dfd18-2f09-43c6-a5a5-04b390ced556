import { Hero } from "@/components/ui/animated-hero";
import { NavbarDemo } from "@/components/ui/navbar-demo";
import { BentoDemo } from "@/components/ui/bento-demo";
import { CarouselDemo } from "@/components/ui/carousel-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { FAQDemo } from "@/components/ui/faq-demo";
import { BlogDemo } from "@/components/ui/blog-demo";
import { Footer2Demo } from "@/components/ui/footer2-demo";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <NavbarDemo />
      <Hero />
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Services</h2>
            <p className="text-xl text-muted-foreground">Discover what we offer</p>
          </div>
          <BentoDemo />
        </div>
      </section>
      <section className="py-20 px-4 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Case Studies</h2>
            <p className="text-xl text-muted-foreground">Explore our featured projects</p>
          </div>
          <CarouselDemo />
        </div>
      </section>
      <TestimonialsDemo />
      <CTADemo />
      <FAQDemo />
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">Featured</h2>
            <p className="text-xl text-muted-foreground">Latest insights and updates</p>
          </div>
          <BlogDemo />
        </div>
      </section>
      <Footer2Demo />
    </div>
  );
};

export default Index;
